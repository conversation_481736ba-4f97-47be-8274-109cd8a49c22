<!DOCTYPE html><html><head>
      <title>RS485_Protocol_For_FPGA</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.19\crossnote\dependencies\katex\katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="rs485-pcfpga-communication-protocol-mvp-version">RS485 PC–FPGA Communication Protocol (MVP Version) </h1>
<h4 id="sept-2025-version">Sept 2025 Version </h4>
<p>This document defines a streamlined protocol for PC-side RS485 driver communication with FPGA devices. Designed for MVP development with minimal workload and concise protocol specification.</p>
<h2 id="supported-command-scope">Supported Command Scope </h2>
<ul>
<li>System commands: S001 (Set slave address)</li>
<li>User commands: U001–U006 (Device configuration parameters)</li>
<li>12-byte payload: First 4 bytes ASCII command key, last 8 bytes data (little-endian)</li>
<li><strong>Bidirectional communication</strong>: PC sends commands, FPGA must return acknowledgment</li>
<li>FRAM persistent storage</li>
</ul>
<h2 id="1-physical-and-link-layer">1. Physical and Link Layer </h2>
<ul>
<li>
<p><strong>Medium</strong>: RS485 (2-wire A/B)</p>
</li>
<li>
<p><strong>Baud rate</strong>: 9600</p>
</li>
<li>
<p><strong>UART framing</strong>: 8N1 (8 data bits, no parity, 1 stop), LSB-first; bytes arrive serially and are processed as a byte stream</p>
</li>
<li>
<p><strong>Frame structure</strong>: 16 bytes fixed length</p>
<ul>
<li>Header: 1 byte = 0xAA</li>
<li>ID byte: 1 byte = [Function code(3 bits) | Slave address(5 bits)]</li>
<li>Payload: 12 bytes (see section 2)</li>
<li>CRC8: 1 byte — polynomial 0x97, initial 0x00, covers 13 bytes (ID + payload)</li>
<li>Trailer: 1 byte = 0x0D</li>
</ul>
</li>
</ul>
<h3 id="function-code-table">Function Code Table </h3>
<table>
<thead>
<tr>
<th>Function Code</th>
<th>Hex</th>
<th>Direction</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>0b111</td>
<td>0x07</td>
<td>PC→FPGA</td>
<td>Assign Data (send command)</td>
</tr>
<tr>
<td>0b010</td>
<td>0x02</td>
<td>FPGA→PC</td>
<td>Response to Assign (acknowledgment)</td>
</tr>
<tr>
<td>0b000</td>
<td>0x00</td>
<td>FPGA→PC</td>
<td>Re-send request (CRC error)</td>
</tr>
</tbody>
</table>
<p><strong>Slave address range</strong>: 1..31; broadcast uses 0x00</p>
<p><strong>Important</strong>: CRC8 covers only 13 bytes (ID byte + 12-byte payload), excluding header and trailer.</p>
<h2 id="2-payload-format-12-bytes">2. Payload Format (12 bytes) </h2>
<ul>
<li>Bytes [0..3]: ASCII command key, e.g., 'S','0','0','1' or 'U','0','0','1'</li>
<li>Bytes [4..11]: 8 bytes binary data, <strong>little-endian encoding</strong>
<ul>
<li><strong>Single 16-bit parameter</strong>: Use bytes [4..5]; bytes [6..11] = 0</li>
<li><strong>Dual 16-bit parameters</strong> (e.g., GPIO channel+enable): Use bytes [4..5] and [6..7]; bytes [8..11] = 0</li>
<li><strong>Maintain 12-byte payload</strong>: For protocol uniformity, always use 12-byte payload</li>
</ul>
</li>
</ul>
<p><strong>Data type note</strong>: Current parameter values are small, 16-bit integers are sufficient. For floating-point needs, use fixed-point mapping (e.g., actual value × 1000 stored as integer).</p>
<h3 id="endianness-mvp">Endianness (MVP) </h3>
<ul>
<li>All numeric fields use little-endian byte order.</li>
<li><strong>Rationale</strong>: PC (x86/x64/Windows on ARM) and typical FPGA/MCU soft-cores are little-endian; this minimizes byte swaps and logic complexity in both PC driver and FPGA implementation.</li>
<li><strong>Example</strong>: Value 250 (0x00FA) → stored as [0xFA, 0x00]; Value 0x1234 → stored as [0x34, 0x12]</li>
</ul>
<h2 id="3-command-set-s001-u001u006">3. Command Set (S001, U001–U006) </h2>
<p>PC sends commands using function code 0b111 (Assign Data). FPGA must return acknowledgment after receiving commands, using function code 0b010 (Response to Assign).</p>
<p><strong>ACK payload</strong>: The FPGA must echo exactly the 12-byte payload it received (ASCII key + data bytes). The PC verifies that the echoed payload matches what was sent. If mismatch or timeout, the PC retransmits the command.</p>
<h3 id="system-configuration-commands">System Configuration Commands </h3>
<ul>
<li><strong>S001 — Set Slave Address</strong>
<ul>
<li>Key: 'S','0','0','1'</li>
<li>Addressing: broadcast (ID address bits = 0)</li>
<li>Payload [4..5]: uint16 newAddress (1..31), little-endian; [6..11] = 0</li>
<li>Action: update device runtime slave address and store to FRAM</li>
</ul>
</li>
</ul>
<p>⚠️ <strong>CRITICAL WARNING</strong>: When executing S001 command, ensure RS485 bus has ONLY ONE slave device connected. Multiple devices will cause address conflicts and communication failure.</p>
<h3 id="user-configuration-commands">User Configuration Commands </h3>
<ul>
<li>
<p><strong>U001 — Set SEL Threshold Current (mA)</strong></p>
<ul>
<li>Key: 'U','0','0','1'</li>
<li>Payload [4..5]: uint16 threshold_mA (40..500); [6..11] = 0</li>
<li>Action: store to FRAM; apply to SEL logic</li>
</ul>
</li>
<li>
<p><strong>U002 — Set SEL Max Amplitude (mA)</strong></p>
<ul>
<li>Key: 'U','0','0','2'</li>
<li>Payload [4..5]: uint16 amplitude_mA (1000..2000); [6..11] = 0</li>
<li>Action: store to FRAM; apply to SEL logic</li>
</ul>
</li>
<li>
<p><strong>U003 — Set SEL Detection Count</strong></p>
<ul>
<li>Key: 'U','0','0','3'</li>
<li>Payload [4..5]: uint16 count (1..5); [6..11] = 0</li>
<li>Action: store to FRAM; consecutive detections before triggering power cycle</li>
</ul>
</li>
<li>
<p><strong>U004 — Set Power Cycle Duration (ms)</strong></p>
<ul>
<li>Key: 'U','0','0','4'</li>
<li>Payload [4..5]: uint16 duration_ms, must be one of {200, 400, 600, 800, 1000}; [6..11] = 0</li>
<li>Action: store to FRAM; used for power cycle control</li>
</ul>
</li>
<li>
<p><strong>U005 — Configure GPIO Input Channel</strong></p>
<ul>
<li>Key: 'U','0','0','5'</li>
<li>Payload [4..5]: uint16 channel (0 or 1)</li>
<li>Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0</li>
<li>Action: store to FRAM; apply to GPIO input configuration</li>
</ul>
</li>
<li>
<p><strong>U006 — Configure GPIO Output Channel</strong></p>
<ul>
<li>Key: 'U','0','0','6'</li>
<li>Payload [4..5]: uint16 channel (0 or 1)</li>
<li>Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0</li>
<li>Action: store to FRAM; apply to GPIO output configuration</li>
</ul>
</li>
</ul>
<p><strong>GPIO Function Note</strong>: GPIO Enable/Disable toggles the logical functionality (digital input/output path) under FPGA software control. It does NOT change physical pin direction if the bitstream fixes it at synthesis time. If runtime pin-direction control is not supported, U005/U006 act as logical masks only. Values are persisted in FRAM.</p>
<h3 id="parameter-range-reference-table">Parameter Range Reference Table </h3>
<p>PC validates all parameters against these ranges before transmission. FPGA receives only pre-validated data.</p>
<table>
<thead>
<tr>
<th>Command</th>
<th>Parameter</th>
<th>Data Type</th>
<th>Valid Range</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>S001</strong></td>
<td>Slave Address</td>
<td>uint16</td>
<td>1 to 31</td>
</tr>
<tr>
<td><strong>U001</strong></td>
<td>SEL Threshold Current</td>
<td>uint16</td>
<td>40 to 500 (mA)</td>
</tr>
<tr>
<td><strong>U002</strong></td>
<td>SEL Max Amplitude</td>
<td>uint16</td>
<td>1000 to 2000 (mA)</td>
</tr>
<tr>
<td><strong>U003</strong></td>
<td>SEL Detection Count</td>
<td>uint16</td>
<td>1 to 5</td>
</tr>
<tr>
<td><strong>U004</strong></td>
<td>Power Cycle Duration</td>
<td>uint16</td>
<td>{200, 400, 600, 800, 1000} (ms)</td>
</tr>
<tr>
<td><strong>U005</strong></td>
<td>GPIO Input Channel</td>
<td>uint16</td>
<td>0 or 1</td>
</tr>
<tr>
<td><strong>U005</strong></td>
<td>GPIO Input Enable</td>
<td>uint16</td>
<td>0 or 1</td>
</tr>
<tr>
<td><strong>U006</strong></td>
<td>GPIO Output Channel</td>
<td>uint16</td>
<td>0 or 1</td>
</tr>
<tr>
<td><strong>U006</strong></td>
<td>GPIO Output Enable</td>
<td>uint16</td>
<td>0 or 1</td>
</tr>
</tbody>
</table>
<h2 id="4-crc8-calculation-polynomial-0x97">4. CRC8 Calculation (polynomial 0x97) </h2>
<ul>
<li><strong>Coverage</strong>: 13 bytes (ID byte + 12-byte payload)</li>
<li><strong>Initial value</strong>: 0x00</li>
<li><strong>Polynomial</strong>: 0x97 ⇒ G(x) = x^8 + x^7 + x^4 + x^2 + x + 1</li>
<li><strong>Algorithm</strong> (Standard CRC8-0x97, MSB first bit processing):<pre data-role="codeBlock" data-info="c" class="language-c c"><code><span class="token class-name">uint8_t</span> <span class="token function">crc8_0x97</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token class-name">uint8_t</span><span class="token operator">*</span> data<span class="token punctuation">,</span> <span class="token class-name">size_t</span> length<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token class-name">uint8_t</span> crc <span class="token operator">=</span> <span class="token number">0x00</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token class-name">size_t</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> length<span class="token punctuation">;</span> i<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        crc <span class="token operator">^=</span> data<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> bit <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> bit <span class="token operator">&lt;</span> <span class="token number">8</span><span class="token punctuation">;</span> bit<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>crc <span class="token operator">&amp;</span> <span class="token number">0x80</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                crc <span class="token operator">=</span> <span class="token punctuation">(</span>crc <span class="token operator">&lt;&lt;</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token operator">^</span> <span class="token number">0x97</span><span class="token punctuation">;</span>
            <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
                crc <span class="token operator">&lt;&lt;=</span> <span class="token number">1</span><span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
    <span class="token keyword keyword-return">return</span> crc<span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre></li>
</ul>
<p><strong>Note</strong>: Little-endian byte order (for multi-byte values) and MSB-first bit processing (for CRC calculation) operate at different levels and do not conflict.</p>
<p><strong>Response timeout</strong>: 100ms (PC waits for ACK after sending command)<br>
<strong>CRC error handling</strong>: FPGA sends resend request; PC automatically retries up to 3 times, then requires manual user intervention</p>
<h2 id="5-communication-flow">5. Communication Flow </h2>
<p><strong>IMPORTANT for S001 Address Setting</strong>: When setting slave address using S001 command, ensure ONLY ONE slave device is connected to the RS485 bus to prevent address conflicts.</p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>PC Side                             FPGA Side
  |                                     |
  |------ Send Command --------------&gt;  |
  |    (Function 0b111)                 |
  |                                     |-- Receive 16 bytes
  |                                     |-- Verify header(0xAA) and trailer(0x0D)
  |                                     |-- Only start to read after header byte
  |                                     |-- Calculate and verify CRC8
  |                                     |
  |                                     |-- CRC error?
  |   &lt;---- Resend Request ----------   |   Yes: Send function 0b000 (re-send request)
  |                                     |
  |                                     |-- CRC correct?
  |                                     |   Yes: Check address
  |                                     |
  |                                     |-- Address match?
  |                                     |   Yes: Execute command &amp; store to FRAM
  |                                     |   No: Ignore (silent)
  |                                     |
  |   &lt;---- Return ACK -------------    |-- Send acknowledgment
  |    (Function 0b010)                 |   (Echo received payload)
  |                                     |
  |-- Verify returned content           |
  |-- Content correct: Complete         |
  |-- Content error/timeout: Resend     |
</code></pre><h3 id="key-process-steps">Key Process Steps: </h3>
<ol>
<li><strong>Frame Sync</strong>: All devices monitor RS485 bus, start reading only after header (0xAA)</li>
<li><strong>Complete Reception</strong>: Read full 16 bytes, confirm trailer (0x0D)</li>
<li><strong>CRC Verification</strong>: Calculate 13-byte CRC, send resend request if mismatch</li>
<li><strong>Address Filtering</strong>: After CRC passes, check address, only matching devices continue</li>
<li><strong>Command Execution</strong>: Store parameters to FRAM and apply settings (PC has pre-validated all data)</li>
<li><strong>Acknowledgment Response</strong>: Send acknowledgment with echoed payload to PC</li>
<li><strong>Content Verification</strong>: PC verifies returned command content to ensure correct communication</li>
</ol>
<h3 id="fpga-decision-flowchart">FPGA Decision Flowchart </h3>
<pre data-role="codeBlock" data-info="" class="language-text"><code>Start: Monitor RS485 Bus
         |
         v
    Detect Header (0xAA)?
         |
    No ──┴── Continue Monitoring
         |
    Yes  v
    Read 16 bytes complete?
         |
    No ──┴── Discard &amp; Continue Monitoring
         |
    Yes  v
    Trailer (0x0D) correct?
         |
    No ──┴── Discard &amp; Continue Monitoring
         |
    Yes  v
    Calculate CRC8 over 13 bytes
         |
         v
    CRC8 matches?
         |
    No ──┴── Send Resend Request (Function 0b000)
         |    └── Continue Monitoring
    Yes  v
    Extract address from ID byte
         |
         v
    Address matches this device?
         |
    No ──┴── Silent Ignore &amp; Continue Monitoring
         |
    Yes  v
    Execute Command:
    - Store to FRAM (PC pre-validated data)
    - Apply settings
         |
         v
    Send ACK (Function 0b010):
    - Echo received payload
    - Calculate new CRC8
         |
         v
    Continue Monitoring
</code></pre><p><strong>Manual Resend Process</strong>: When PC times out (no ACK received after 3 automatic retries for CRC errors), user must manually resend command via GUI interface. This indicates potential link connectivity issues.</p>
<h2 id="6-frame-format-examples">6. Frame Format Examples </h2>
<h3 id="pc-command-frame-function-0b111">PC Command Frame (Function 0b111) </h3>
<p><strong>Example: S001 set slave address to 1</strong></p>
<ul>
<li>Hex: <code>AA E0 53 30 30 31 01 00 00 00 00 00 00 00 0B 0D</code></li>
<li>Breakdown:
<ul>
<li>Header: 0xAA</li>
<li>ID: 0xE0 (0b11100000 = Function 0b111 + Address 0b00000)</li>
<li>Payload: "S001" + value 1 (little-endian) + padding zeros</li>
<li>CRC8: 0x0B (calculated over 13 bytes: E0 53 30 30 31 01 00 00 00 00 00 00 00)</li>
<li>Trailer: 0x0D</li>
</ul>
</li>
</ul>
<p><strong>Example: U001 set threshold to 250mA</strong></p>
<ul>
<li>Hex: <code>AA E1 55 30 30 31 FA 00 00 00 00 00 00 00 52 0D</code></li>
<li>Breakdown:
<ul>
<li>Header: 0xAA</li>
<li>ID: 0xE1 (0b11100001 = Function 0b111 + Address 0b00001)</li>
<li>Payload: "U001" + value 250 (0x00FA little-endian) + padding zeros</li>
<li>CRC8: 0x52</li>
<li>Trailer: 0x0D</li>
</ul>
</li>
</ul>
<p><strong>Example: U005 enable GPIO input channel 0</strong></p>
<ul>
<li>Hex: <code>AA E1 55 30 30 35 00 00 01 00 00 00 00 00 E9 0D</code></li>
<li>Breakdown:
<ul>
<li>Header: 0xAA</li>
<li>ID: 0xE1 (0b11100001 = Function 0b111 + Address 0b00001)</li>
<li>Payload: "U005" + channel 0 (0x0000) + enable 1 (0x0001) + padding zeros</li>
<li>CRC8: 0xE9</li>
<li>Trailer: 0x0D</li>
</ul>
</li>
</ul>
<p><strong>Example: U006 disable GPIO output channel 1</strong></p>
<ul>
<li>Hex: <code>AA E1 55 30 30 36 01 00 00 00 00 00 00 00 92 0D</code></li>
<li>Breakdown:
<ul>
<li>Header: 0xAA</li>
<li>ID: 0xE1 (0b11100001 = Function 0b111 + Address 0b00001)</li>
<li>Payload: "U006" + channel 1 (0x0001) + enable 0 (0x0000) + padding zeros</li>
<li>CRC8: 0x92</li>
<li>Trailer: 0x0D</li>
</ul>
</li>
</ul>
<h3 id="fpga-acknowledgment-frame-function-0b010">FPGA Acknowledgment Frame (Function 0b010) </h3>
<p><strong>Example: ACK for S001 command</strong></p>
<ul>
<li>Hex: <code>AA 40 53 30 30 31 01 00 00 00 00 00 00 00 BA 0D</code></li>
<li>Breakdown:
<ul>
<li>Header: 0xAA</li>
<li>ID: 0x40 (0b01000000 = Function 0b010 + Address 0b00000)</li>
<li>Payload: Echo of received payload (identical to PC command)</li>
<li>CRC8: 0xBA</li>
<li>Trailer: 0x0D</li>
</ul>
</li>
</ul>
<h3 id="fpga-resend-request-frame-function-0b000">FPGA Resend Request Frame (Function 0b000) </h3>
<p><strong>Example: Resend request from slave address 1</strong></p>
<ul>
<li>Hex: <code>AA 01 00 00 00 00 00 00 00 00 00 00 00 00 AB 0D</code></li>
<li>Breakdown:
<ul>
<li>Header: 0xAA</li>
<li>ID: 0x01 (0b00000001 = Function 0b000 + Address 0b00001)</li>
<li>Payload: All zeros (12 bytes)</li>
<li>CRC8: 0xAB</li>
<li>Trailer: 0x0D</li>
</ul>
</li>
</ul>
<h3 id="recommended-initusage-flow-mvp">Recommended Init/Usage Flow (MVP) </h3>
<ol>
<li>PC opens COM port (FTDI VCP) and initializes buffers.</li>
<li>FPGA powers up with defaults; loads persisted config from FRAM (S001, U001–U006).</li>
<li>Configure system:
<ul>
<li><strong>S001: set slave address</strong> (IMPORTANT: Only ONE slave device connected during address assignment)</li>
</ul>
</li>
<li>Normal operation: non-blocking request/ACK loop with retry on timeout.</li>
</ol>
<h3 id="multi-device-behavior-and-errors">Multi-device Behavior and Errors </h3>
<ul>
<li><strong>Addressing</strong>: only the device whose address matches the ID byte processes the command; others ignore it and stay idle.</li>
<li><strong>Wrong-address</strong>: PC times out waiting for the intended device; retransmit as needed. The bus remains free.</li>
<li><strong>CRC failure</strong>: device sends re-send request (function code 0b000) immediately; PC retransmits the last command.</li>
<li><strong>Data loss</strong>: No response from FPGA; PC times out and retries up to 3 times, then requires manual intervention to check link connectivity.</li>
</ul>
<h2 id="7-fram-storage">7. FRAM Storage </h2>
<ul>
<li><strong>Purpose</strong>: Persistent storage of all configuration parameters</li>
<li><strong>Address allocation</strong>: Decided by FPGA side (Junkai) for address mapping</li>
<li><strong>Storage content</strong>: S001 slave address, U001-U006 all user configuration parameters</li>
<li><strong>Data format</strong>: little-endian, consistent with protocol</li>
<li><strong>Power-on loading</strong>: FPGA reads all configuration parameters from FRAM at startup</li>
<li><strong>Write optimization</strong>: Only write to FRAM when parameter value differs from current value (extends FRAM lifespan)</li>
<li><strong>Frame synchronization recovery</strong>: If frame reception error occurs, FPGA discards current frame and searches for next 0xAA header</li>
</ul>
<h2 id="8-error-handling">8. Error Handling </h2>
<ul>
<li><strong>CRC error</strong>: FPGA sends resend request (function code 0b000); PC automatically retries up to 3 times, then requires manual user intervention</li>
<li><strong>Address mismatch</strong>: Silent ignore (no response sent)</li>
<li><strong>Data loss/No response</strong>: PC timeout after 100ms; PC retries up to 3 times, then requires manual intervention to check link connectivity</li>
<li><strong>Timeout handling</strong>: 100ms timeout; after 3 automatic retries, user must manually resend via GUI interface</li>
</ul>
<p><strong>Simplified Response Strategy</strong>:</p>
<ul>
<li>PC validates all parameter ranges before transmission</li>
<li>FPGA receives only pre-validated data from PC</li>
<li>FPGA has only two responses: ACK (received and executed) or Resend Request (CRC error)</li>
<li>No silent ignore for valid frames - FPGA always responds to correctly addressed frames with valid CRC</li>
<li><strong>Manual intervention required</strong>: Only after 3 failed automatic retries, indicating potential link connectivity issues</li>
</ul>
<h3 id="default-parameter-values-first-bootto-be-confirmed">Default Parameter Values (First Boot)（To be confirmed） </h3>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Default Value</th>
<th>Rationale</th>
</tr>
</thead>
<tbody>
<tr>
<td>Slave Address</td>
<td>1</td>
<td>Safe default for single device</td>
</tr>
<tr>
<td>SEL Threshold</td>
<td>100 mA</td>
<td>Conservative middle value</td>
</tr>
<tr>
<td>SEL Max Amplitude</td>
<td>1500 mA</td>
<td>Conservative middle value</td>
</tr>
<tr>
<td>SEL Detection Count</td>
<td>3</td>
<td>Balanced sensitivity</td>
</tr>
<tr>
<td>Power Cycle Duration</td>
<td>600 ms</td>
<td>Standard duration</td>
</tr>
<tr>
<td>GPIO Input/Output</td>
<td>All disabled</td>
<td>Safe default state</td>
</tr>
</tbody>
</table>
<h2 id="9-quick-start-checklist">9. Quick Start Checklist </h2>
<h3 id="initial-setup">Initial Setup </h3>
<ol>
<li>Connect ONLY ONE slave device to RS485 bus</li>
<li>Send S001 command to set slave address (uses broadcast address 0x00)</li>
<li>Verify ACK response with echoed payload</li>
<li>Configure parameters using U001-U006 commands</li>
</ol>
<h3 id="key-implementation-points">Key Implementation Points </h3>
<ul>
<li><strong>Frame sync</strong>: Start reading after 0xAA header, discard on error</li>
<li><strong>CRC8</strong>: Use standard 0x97 polynomial algorithm (see Section 4)</li>
<li><strong>No parameter validation needed</strong>: PC pre-validates all data before transmission</li>
<li><strong>FRAM optimization</strong>: Only write when value changes</li>
<li><strong>Error handling</strong>: 3 automatic retries for CRC errors, manual retry for connectivity issues</li>
<li><strong>Always respond</strong>: FPGA must respond to all correctly addressed frames (ACK or Resend Request only)</li>
</ul>
<p>This protocol is MVP version, maintaining minimal complexity to ensure minimum development workload for both PC and FPGA sides.</p>

      </div>
      
      
    
    
    
    
    
    
  
    </body></html>