# RS485 PC–FPGA Communication Protocol (MVP Version)
#### January 2025 Version

This document defines a streamlined protocol for PC-side RS485 driver communication with FPGA devices. Designed for MVP development with minimal workload and concise protocol specification.

## Supported Command Scope
- System commands: S001 (Set slave address)
- User commands: U001–U006 (Device configuration parameters)
- 12-byte payload: First 4 bytes ASCII command key, last 8 bytes data (little-endian)
- **Bidirectional communication**: PC sends commands, FPGA must return acknowledgment
- FRAM persistent storage

## 1. Physical and Link Layer
- **Medium**: RS485 (2-wire A/B)
- **Baud rate**: 9600 bps (default)
- **Frame structure**: 16 bytes fixed length
  - Header: 1 byte = 0xAA
  - ID byte: 1 byte = [Function code(3 bits) | Slave address(5 bits)]
    - PC sends command: 0b111 (Assign Data)
    - FPGA returns acknowledgment: 0b010 (Response to Assign)
    - Slave address range: 1..31; broadcast uses 0x00
  - Payload: 12 bytes (see section 2)
  - CRC8: 1 byte — polynomial 0x97, initial 0x00, covers 13 bytes (ID + payload)
  - Trailer: 1 byte = 0x0D

**Important**: CRC8 covers only 13 bytes (ID byte + 12-byte payload), excluding header and trailer.

## 2. Payload Format (12 bytes)
- Bytes [0..3]: ASCII command key, e.g., 'S','0','0','1' or 'U','0','0','1'
- Bytes [4..11]: 8 bytes binary data, **little-endian encoding**
  - **Single 16-bit parameter**: Use bytes [4..5]; bytes [6..11] = 0
  - **Dual 16-bit parameters** (e.g., GPIO channel+enable): Use bytes [4..5] and [6..7]; bytes [8..11] = 0
  - **Maintain 12-byte payload**: For protocol uniformity, always use 12-byte payload

**Data type note**: Current parameter values are small, 16-bit integers are sufficient. For floating-point needs, use fixed-point mapping (e.g., actual value × 1000 stored as integer).

## 3. Command Set (S001, U001–U006)

PC sends commands using function code 0b111 (Assign Data). FPGA must return acknowledgment after receiving commands, using function code 0b010 (Response to Assign).

### System Configuration Commands
- **S001 — Set Slave Address**
  - Key: 'S','0','0','1'
  - Addressing: broadcast (ID address bits = 0)
  - Payload [4..5]: uint16 newAddress (1..31), little-endian; [6..11] = 0
  - Action: update device runtime slave address and store to FRAM

### User Configuration Commands
- **U001 — Set SEL Threshold Current (mA)**
  - Key: 'U','0','0','1'
  - Payload [4..5]: uint16 threshold_mA (40..500); [6..11] = 0
  - Action: store to FRAM; apply to SEL logic

- **U002 — Set SEL Max Amplitude (mA)**
  - Key: 'U','0','0','2'
  - Payload [4..5]: uint16 amplitude_mA (1000..2000); [6..11] = 0
  - Action: store to FRAM; apply to SEL logic

- **U003 — Set SEL Detection Count**
  - Key: 'U','0','0','3'
  - Payload [4..5]: uint16 count (1..5); [6..11] = 0
  - Action: store to FRAM; consecutive detections before triggering power cycle

- **U004 — Set Power Cycle Duration (ms)**
  - Key: 'U','0','0','4'
  - Payload [4..5]: uint16 duration_ms, must be one of {200, 400, 600, 800, 1000}; [6..11] = 0
  - Action: store to FRAM; used for power cycle control

- **U005 — Configure GPIO Input Channel**
  - Key: 'U','0','0','5'
  - Payload [4..5]: uint16 channel (0 or 1)
  - Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0
  - Action: store to FRAM; apply to GPIO input configuration

- **U006 — Configure GPIO Output Channel**
  - Key: 'U','0','0','6'
  - Payload [4..5]: uint16 channel (0 or 1)
  - Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0
  - Action: store to FRAM; apply to GPIO output configuration

**GPIO Function Note**: GPIO Enable/Disable refers to GPIO functionality switch, controlled by FPGA software for digital input/output functions.

**Little-endian Assembly**: If HDL reads bytes serially, assemble 16-bit values as v = b0 + (b1<<8).

## 4. CRC8 Calculation (polynomial 0x97)
- **Coverage**: 13 bytes (ID byte + 12-byte payload)
- **Initial value**: 0x00
- **Polynomial**: 0x97 ⇒ G(x) = x^8 + x^7 + x^4 + x^2 + x + 1
- **Algorithm** (LSB first):
  ```
  For each input byte:
    For 8 bits:
      mix = (crc ^ byte) & 0x01
      crc >>= 1
      if (mix) crc ^= 0x97
      byte >>= 1
  ```

PC driver uses this algorithm, FPGA must implement the same algorithm to ensure compatibility.

## 5. Communication Flow

```
PC Side                        FPGA Side
  |                              |
  |------ Send Command ------>   |
  |    (Function 0b111)          |
  |                              |-- Receive 16 bytes
  |                              |-- Verify header(0xAA) and trailer(0x0D)
  |                              |-- Calculate and verify CRC8
  |                              |
  |                              |-- CRC error?
  |   <---- Resend Request ------|   Yes: Send resend request
  |                              |
  |                              |-- CRC correct?
  |                              |   Yes: Check address
  |                              |
  |                              |-- Address match?
  |                              |   Yes: Execute command
  |                              |   No: Ignore
  |                              |
  |   <---- Return ACK ---------|-- Send acknowledgment
  |    (Function 0b010)          |   (Contains received command)
  |                              |
  |-- Verify returned content    |
  |-- Content correct: Complete  |
  |-- Content error/timeout: Resend |
```

### Key Process Steps:
1. **Frame Sync**: All devices monitor RS485 bus, start reading only after header (0xAA)
2. **Complete Reception**: Read full 16 bytes, confirm trailer (0x0D)
3. **CRC Verification**: Calculate 13-byte CRC, send resend request if mismatch
4. **Address Filtering**: After CRC passes, check address, only matching devices execute
5. **Acknowledgment Response**: After executing command, FPGA must send acknowledgment to PC
6. **Content Verification**: PC verifies returned command content to ensure correct communication

## 6. FRAM Storage
- **Purpose**: Persistent storage of all configuration parameters
- **Address allocation**: Decided by FPGA side (Junkai) for address mapping
- **Storage content**:
  - S001 slave address
  - U001-U006 all user configuration parameters
- **Data format**: little-endian, consistent with protocol
- **Power-on loading**: FPGA reads all configuration parameters from FRAM at startup

## 7. Error Handling
- **CRC error**: FPGA sends resend request (function code 0b000)
- **Address mismatch**: Silent ignore (no response sent)
- **Parameter out of range**: Silent ignore or send error response
- **Timeout handling**: PC resends command after timeout

## 8. Implementation Points
1. **UART Reception**: Parse 16-byte frame, confirm header and trailer
2. **CRC Calculation**: Calculate CRC8 over 13 bytes (ID + payload)
3. **Function Code Processing**:
   - Receive: 0b111 (Assign Data)
   - Send: 0b010 (Response to Assign)
4. **Address Matching**: Extract lower 5 bits of ID byte, compare with device address
5. **Payload Parsing**: Extract ASCII key and binary data
6. **FRAM Operation**: Immediately store configuration parameters
7. **Response Sending**: Construct acknowledgment frame and send

This protocol is MVP version, maintaining minimal complexity to ensure minimum development workload for both PC and FPGA sides.
