# RS485 PC–FPGA Communication Protocol (MVP Version)
#### Sept 2025 Version

This document defines a streamlined protocol for PC-side RS485 driver communication with FPGA devices. Designed for MVP development with minimal workload and concise protocol specification.

## Supported Command Scope
- System commands: S001 (Set slave address)
- User commands: U001–U006 (Device configuration parameters)
- 12-byte payload: First 4 bytes ASCII command key, last 8 bytes data (little-endian)
- **Bidirectional communication**: PC sends commands, FPGA must return acknowledgment
- FRAM persistent storage

## 1. Physical and Link Layer
- **Medium**: RS485 (2-wire A/B)
- **Baud rate**: 9600
- **UART framing**: 8N1 (8 data bits, no parity, 1 stop), LSB-first; bytes arrive serially and are processed as a byte stream

- **Frame structure**: 16 bytes fixed length
  - Header: 1 byte = 0xAA
  - ID byte: 1 byte = [Function code(3 bits) | Slave address(5 bits)]
  - Payload: 12 bytes (see section 2)
  - CRC8: 1 byte — polynomial 0x97, initial 0x00, covers 13 bytes (ID + payload)
  - Trailer: 1 byte = 0x0D

### Function Code Table
| Function Code |Hex  |  Direction | Description|  
|---------------|--------|-----|-----------|
| 0b111 | 0x07 | PC→FPGA | Assign Data (send command) |
| 0b010 | 0x02 | FPGA→PC | Response to Assign (acknowledgment) |
| 0b000 | 0x00 | FPGA→PC | Re-send request (CRC error) |

**Slave address range**: 1..31; broadcast uses 0x00

**Important**: CRC8 covers only 13 bytes (ID byte + 12-byte payload), excluding header and trailer.

## 2. Payload Format (12 bytes)
- Bytes [0..3]: ASCII command key, e.g., 'S','0','0','1' or 'U','0','0','1'
- Bytes [4..11]: 8 bytes binary data, **little-endian encoding**
  - **Single 16-bit parameter**: Use bytes [4..5]; bytes [6..11] = 0
  - **Dual 16-bit parameters** (e.g., GPIO channel+enable): Use bytes [4..5] and [6..7]; bytes [8..11] = 0
  - **Maintain 12-byte payload**: For protocol uniformity, always use 12-byte payload

**Data type note**: Current parameter values are small, 16-bit integers are sufficient. For floating-point needs, use fixed-point mapping (e.g., actual value × 1000 stored as integer).

### Endianness (MVP)
- All numeric fields use little-endian byte order.
- **Rationale**: PC (x86/x64/Windows on ARM) and typical FPGA/MCU soft-cores are little-endian; this minimizes byte swaps and logic complexity in both PC driver and FPGA implementation.
- **Example**: Value 250 (0x00FA) → stored as [0xFA, 0x00]; Value 0x1234 → stored as [0x34, 0x12]

## 3. Command Set (S001, U001–U006)

PC sends commands using function code 0b111 (Assign Data). FPGA must return acknowledgment after receiving commands, using function code 0b010 (Response to Assign).

**ACK payload**: The FPGA must echo exactly the 12-byte payload it received (ASCII key + data bytes). The PC verifies that the echoed payload matches what was sent. If mismatch or timeout, the PC retransmits the command.

### System Configuration Commands
- **S001 — Set Slave Address**
  - Key: 'S','0','0','1'
  - Addressing: broadcast (ID address bits = 0)
  - Payload [4..5]: uint16 newAddress (1..31), little-endian; [6..11] = 0
  - Action: update device runtime slave address and store to FRAM

⚠️ **CRITICAL WARNING**: When executing S001 command, ensure RS485 bus has ONLY ONE slave device connected. Multiple devices will cause address conflicts and communication failure.

### User Configuration Commands
- **U001 — Set SEL Threshold Current (mA)**
  - Key: 'U','0','0','1'
  - Payload [4..5]: uint16 threshold_mA (40..500); [6..11] = 0
  - Action: store to FRAM; apply to SEL logic
  - **Validation**: PC validates range; FPGA silently ignores if out of range

- **U002 — Set SEL Max Amplitude (mA)**
  - Key: 'U','0','0','2'
  - Payload [4..5]: uint16 amplitude_mA (1000..2000); [6..11] = 0
  - Action: store to FRAM; apply to SEL logic
  - **Validation**: PC validates range; FPGA silently ignores if out of range

- **U003 — Set SEL Detection Count**
  - Key: 'U','0','0','3'
  - Payload [4..5]: uint16 count (1..5); [6..11] = 0
  - Action: store to FRAM; consecutive detections before triggering power cycle
  - **Validation**: PC validates range; FPGA silently ignores if out of range

- **U004 — Set Power Cycle Duration (ms)**
  - Key: 'U','0','0','4'
  - Payload [4..5]: uint16 duration_ms, must be one of {200, 400, 600, 800, 1000}; [6..11] = 0
  - Action: store to FRAM; used for power cycle control
  - **Validation**: PC validates range; FPGA silently ignores if out of range

- **U005 — Configure GPIO Input Channel**
  - Key: 'U','0','0','5'
  - Payload [4..5]: uint16 channel (0 or 1)
  - Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0
  - Action: store to FRAM; apply to GPIO input configuration
  - **Validation**: PC validates range; FPGA silently ignores if out of range

- **U006 — Configure GPIO Output Channel**
  - Key: 'U','0','0','6'
  - Payload [4..5]: uint16 channel (0 or 1)
  - Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0
  - Action: store to FRAM; apply to GPIO output configuration
  - **Validation**: PC validates range; FPGA silently ignores if out of range

**GPIO Function Note**: GPIO Enable/Disable toggles the logical functionality (digital input/output path) under FPGA software control. It does NOT change physical pin direction if the bitstream fixes it at synthesis time. If runtime pin-direction control is not supported, U005/U006 act as logical masks only. Values are persisted in FRAM.

### Parameter Range Validation Table

Both PC and FPGA must validate parameters against these ranges. FPGA silently ignores commands with out-of-range parameters.

| Command | Parameter | Data Type | Valid Range | Invalid Behavior |
|---------|-----------|-----------|-------------|------------------|
| **S001** | Slave Address | uint16 | 1 to 31 | FPGA ignores (silent) |
| **U001** | SEL Threshold Current | uint16 | 40 to 500 (mA) | FPGA ignores (silent) |
| **U002** | SEL Max Amplitude | uint16 | 1000 to 2000 (mA) | FPGA ignores (silent) |
| **U003** | SEL Detection Count | uint16 | 1 to 5 | FPGA ignores (silent) |
| **U004** | Power Cycle Duration | uint16 | {200, 400, 600, 800, 1000} (ms) | FPGA ignores (silent) |
| **U005** | GPIO Input Channel | uint16 | 0 or 1 | FPGA ignores (silent) |
| **U005** | GPIO Input Enable | uint16 | 0 or 1 | FPGA ignores (silent) |
| **U006** | GPIO Output Channel | uint16 | 0 or 1 | FPGA ignores (silent) |
| **U006** | GPIO Output Enable | uint16 | 0 or 1 | FPGA ignores (silent) |

**Note**: Parameter validation occurs after CRC verification and address matching. Invalid parameters result in silent ignore (no ACK sent), causing PC timeout. User must manually correct values and resend via GUI interface.

## 4. CRC8 Calculation (polynomial 0x97)
- **Coverage**: 13 bytes (ID byte + 12-byte payload)
- **Initial value**: 0x00
- **Polynomial**: 0x97 ⇒ G(x) = x^8 + x^7 + x^4 + x^2 + x + 1
- **Algorithm** (Standard CRC8-0x97, MSB first):
  ```c
  uint8_t crc8_0x97(const uint8_t* data, size_t length) {
      uint8_t crc = 0x00;
      for (size_t i = 0; i < length; i++) {
          crc ^= data[i];
          for (int bit = 0; bit < 8; bit++) {
              if (crc & 0x80) {
                  crc = (crc << 1) ^ 0x97;
              } else {
                  crc <<= 1;
              }
          }
      }
      return crc;
  }
  ```

**Response timeout**: 100ms (PC waits for ACK after sending command)
**CRC error handling**: FPGA sends resend request; PC automatically retries up to 3 times, then requires manual user intervention

## 5. Communication Flow

**IMPORTANT for S001 Address Setting**: When setting slave address using S001 command, ensure ONLY ONE slave device is connected to the RS485 bus to prevent address conflicts.

```
PC Side                             FPGA Side
  |                                     |
  |------ Send Command -------------->  |
  |    (Function 0b111)                 |
  |                                     |-- Receive 16 bytes
  |                                     |-- Verify header(0xAA) and trailer(0x0D)
  |                                     |-- Only start to read after header byte
  |                                     |-- Calculate and verify CRC8
  |                                     |
  |                                     |-- CRC error?
  |   <---- Resend Request ----------   |   Yes: Send function 0b000 (re-send request)
  |                                     |
  |                                     |-- CRC correct?
  |                                     |   Yes: Check address
  |                                     |
  |                                     |-- Address match?
  |                                     |   Yes: Validate parameter range
  |                                     |   No: Ignore (silent)
  |                                     |
  |                                     |-- Parameter valid?
  |                                     |   Yes: Execute command & store to FRAM
  |                                     |   No: Ignore (silent, no response)
  |                                     |
  |   <---- Return ACK -------------    |-- Send acknowledgment
  |    (Function 0b010)                 |   (Echo received payload)
  |                                     |
  |-- Verify returned content           |
  |-- Content correct: Complete         |
  |-- Content error/timeout: Resend     |
```

### Key Process Steps:
1. **Frame Sync**: All devices monitor RS485 bus, start reading only after header (0xAA)
2. **Complete Reception**: Read full 16 bytes, confirm trailer (0x0D)
3. **CRC Verification**: Calculate 13-byte CRC, send resend request if mismatch
4. **Address Filtering**: After CRC passes, check address, only matching devices continue
5. **Parameter Validation**: Check parameter ranges (see Parameter Range Validation Table), silently ignore if invalid (no response)
6. **Command Execution**: Store valid parameters to FRAM and apply settings
7. **Acknowledgment Response**: Send acknowledgment with echoed payload to PC
8. **Content Verification**: PC verifies returned command content to ensure correct communication

### FPGA Decision Flowchart

```
Start: Monitor RS485 Bus
         |
         v
    Detect Header (0xAA)?
         |
    No ──┴── Continue Monitoring
         |
    Yes  v
    Read 16 bytes complete?
         |
    No ──┴── Discard & Continue Monitoring
         |
    Yes  v
    Trailer (0x0D) correct?
         |
    No ──┴── Discard & Continue Monitoring
         |
    Yes  v
    Calculate CRC8 over 13 bytes
         |
         v
    CRC8 matches?
         |
    No ──┴── Send Resend Request (Function 0b000)
         |    └── Continue Monitoring
    Yes  v
    Extract address from ID byte
         |
         v
    Address matches this device?
         |
    No ──┴── Silent Ignore & Continue Monitoring
         |
    Yes  v
    Extract parameters from payload
         |
         v
    Parameters within valid range?
    (Check Parameter Range Validation Table)
         |
    No ──┴── Silent Ignore & Continue Monitoring
         |
    Yes  v
    Execute Command:
    - Store to FRAM
    - Apply settings
         |
         v
    Send ACK (Function 0b010):
    - Echo received payload
    - Calculate new CRC8
         |
         v
    Continue Monitoring
```

**Manual Resend Process**: When PC times out (no ACK received), user must manually resend command via GUI interface. This allows user to correct invalid parameter values and prevents automatic retry loops that could cause deadlock situations.

## 6. Frame Format Examples

### PC Command Frame (Function 0b111)
**Example: S001 set slave address to 1**
- Hex: `AA E0 53 30 30 31 01 00 00 00 00 00 00 00 8C 0D`
- Breakdown:
  - Header: 0xAA
  - ID: 0xE0 (0b11100000 = Function 0b111 + Address 0b00000)
  - Payload: "S001" + value 1 (little-endian) + padding zeros
  - CRC8: 0x8C (calculated over 13 bytes: E0 53 30 30 31 01 00 00 00 00 00 00 00)
  - Trailer: 0x0D

**Example: U001 set threshold to 250mA**
- Hex: `AA E1 55 30 30 31 FA 00 00 00 00 00 00 00 B7 0D`
- Breakdown:
  - Header: 0xAA
  - ID: 0xE1 (0b11100001 = Function 0b111 + Address 0b00001)
  - Payload: "U001" + value 250 (0x00FA little-endian) + padding zeros
  - CRC8: 0xB7
  - Trailer: 0x0D

**Example: U005 enable GPIO input channel 0**
- Hex: `AA E1 55 30 30 35 00 00 01 00 00 00 00 00 B3 0D`
- Breakdown:
  - Header: 0xAA
  - ID: 0xE1 (0b11100001 = Function 0b111 + Address 0b00001)
  - Payload: "U005" + channel 0 (0x0000) + enable 1 (0x0001) + padding zeros
  - CRC8: 0xB3
  - Trailer: 0x0D

**Example: U006 disable GPIO output channel 1**
- Hex: `AA E1 55 30 30 36 01 00 00 00 00 00 00 00 B4 0D`
- Breakdown:
  - Header: 0xAA
  - ID: 0xE1 (0b11100001 = Function 0b111 + Address 0b00001)
  - Payload: "U006" + channel 1 (0x0001) + enable 0 (0x0000) + padding zeros
  - CRC8: 0xB4
  - Trailer: 0x0D

### FPGA Acknowledgment Frame (Function 0b010)
**Example: ACK for S001 command**
- Hex: `AA 40 53 30 30 31 01 00 00 00 00 00 00 00 0C 0D`
- Breakdown:
  - Header: 0xAA
  - ID: 0x40 (0b01000000 = Function 0b010 + Address 0b00000)
  - Payload: Echo of received payload (identical to PC command)
  - CRC8: 0x0C
  - Trailer: 0x0D

### FPGA Resend Request Frame (Function 0b000)
**Example: Resend request from slave address 1**
- Hex: `AA 01 00 00 00 00 00 00 00 00 00 00 00 00 01 0D`
- Breakdown:
  - Header: 0xAA
  - ID: 0x01 (0b00000001 = Function 0b000 + Address 0b00001)
  - Payload: All zeros (12 bytes)
  - CRC8: 0x01
  - Trailer: 0x0D
### Recommended Init/Usage Flow (MVP)
1. PC opens COM port (FTDI VCP) and initializes buffers.
2. FPGA powers up with defaults; loads persisted config from FRAM (S001, U001–U006).
3. Configure system:
   - **S001: set slave address** (IMPORTANT: Only ONE slave device connected during address assignment)
4. Normal operation: non-blocking request/ACK loop with retry on timeout.

### Multi-device Behavior and Errors
- **Addressing**: only the device whose address matches the ID byte processes the command; others ignore it and stay idle.
- **Wrong-address**: PC times out waiting for the intended device; retransmit as needed. The bus remains free.
- **CRC failure**: device sends re-send request (function code 0b000) immediately; PC retransmits the last command.

## 7. FRAM Storage
- **Purpose**: Persistent storage of all configuration parameters
- **Address allocation**: Decided by FPGA side (Junkai) for address mapping
- **Storage content**: S001 slave address, U001-U006 all user configuration parameters
- **Data format**: little-endian, consistent with protocol
- **Power-on loading**: FPGA reads all configuration parameters from FRAM at startup
- **Write optimization**: Only write to FRAM when parameter value differs from current value (extends FRAM lifespan)
- **Frame synchronization recovery**: If frame reception error occurs, FPGA discards current frame and searches for next 0xAA header

## 8. Error Handling
- **CRC error**: FPGA sends resend request (function code 0b000); PC automatically retries up to 3 times, then requires manual user intervention
- **Address mismatch**: Silent ignore (no response sent)
- **Parameter out of range**: Silent ignore (no response sent) - prevents deadlock scenarios
- **Timeout handling**: 100ms timeout; after 3 automatic retries for CRC errors, user must manually resend via GUI interface

**Parameter Validation Strategy**:
- PC performs primary range validation before sending commands
- FPGA performs secondary validation after CRC and address checks (see Parameter Range Validation Table)
- If FPGA detects invalid parameters, it silently ignores the command (no ACK, no resend request)
- PC will timeout, requiring user to manually correct values and resend via GUI
- **Manual Resend Requirement**: This prevents automatic retry loops and allows user to verify/correct parameter values, avoiding deadlock situations

### Default Parameter Values (First Boot)（）
| Parameter | Default Value | Rationale |
|-----------|---------------|-----------|
| Slave Address | 1 | Safe default for single device |
| SEL Threshold | 100 mA | Conservative middle value |
| SEL Max Amplitude | 1500 mA | Conservative middle value |
| SEL Detection Count | 3 | Balanced sensitivity |
| Power Cycle Duration | 600 ms | Standard duration |
| GPIO Input/Output | All disabled | Safe default state |

## 9. Quick Start Checklist

### Initial Setup
1. Connect ONLY ONE slave device to RS485 bus
2. Send S001 command to set slave address (uses broadcast address 0x00)
3. Verify ACK response with echoed payload
4. Configure parameters using U001-U006 commands

### Key Implementation Points
- **Frame sync**: Start reading after 0xAA header, discard on error
- **CRC8**: Use standard 0x97 polynomial algorithm (see Section 4)
- **Parameter validation**: Check ranges per validation table, silent ignore if invalid
- **FRAM optimization**: Only write when value changes
- **Error handling**: 3 automatic retries for CRC errors, manual retry for parameter errors

This protocol is MVP version, maintaining minimal complexity to ensure minimum development workload for both PC and FPGA sides.
