help me double check and make sure everything is correct. everything is in English

认真修改 
"D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\RS485Driver_new_python_with_new_UI\protocol_design - G5-modi\RS485_Protocol_For_FPGA.md"

这个文档，我负责 RS485部分，需要负责 FPGA 那边的同事配合好。而且我们这个要做 MVP, 所以尽可能精简

仔细读我们的设计文档，

"D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\RS485Driver_new_python_with_new_UI\protocol_design - G5-modi\RS485 Communication Software Protocol - remove-info.md"
"D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\RS485Driver_new_python_with_new_UI\protocol_design - G5-modi\RS485 Communication Software Protocol_v1.1.docx"
"D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\RS485Driver_new_python_with_new_UI\protocol_design - G5-modi\RS485_Driver_API_Design_Document_Updated.md"

基于我们目前的设计，
就是通过 UART 把 S001, U001-006 这些指令按照格式，传输给 FPGA 这边，然后 FPGA 去按照格式拆包，然后修改我们需要修改的参数

PC 这边每次把指令发给 Slave, 那么 Slave 收到以后，必须要返回一条信息给 PC, 说明 收到指令，并且返回说收到的指令是什么，PC 这边要和这条指令校验。如果是正确的，就不用再反应，如果是错误的，或者一定时间内 PC 这边没有收到 slave 返回的这条信息，PC 就重新发一次

从 slave 发回给 PC 的这条信息的 Code 得使用 0b010, 在我们的文档里有写

其他的部分尽可能精简，因为我们是要做 MVP, 也不希望 FPGA 这边的开发量 unnecessarily 大

然后我们的 CRC 也决定使用 
0x97 ⇒ G(x) = x^8 + x^7 + x^4 + x^2 + x + 1
就按照这个来

---
还有 整合一下 以下的信息
## 2) Endianness — recommend little-endian
- Proposal: “All numeric fields use little-endian unless negotiated otherwise.”
- Rationale: PC (x86/x64/Windows on ARM) and typical FPGA/MCU soft-cores are little-endian; this minimizes swaps and logic.
- Future-proofing: if we ever need big-endian devices, we can add a negotiation bit (or an S-series config) and then unify the entire channel.



## 4) Payload format and avoiding floats — fixed-point mapping and padding
- Payload remains 12 bytes total:
  - [0..3]: ASCII key (e.g., 'S','0','0','1')
  - [4..11]: 8 bytes binary, little-endian
- For single 32-bit parameters: use [4..7]; pad [8..11] with zeros
- For two 32-bit parameters: use [4..7] and [8..11]
- If a command needs only 16-bit integers (as requested for S001 and U001–U006 right now):
  - First value in [4..5], second (if any) in [6..7], and pad the rest ([8..11]) with zeros
  - We will still maintain the 12-byte payload and framing for uniformity
- Floating-point avoidance: use fixed-point integers
  - Example rule of thumb: define per-parameter scaling (e.g., store value_in_units × 1000 as int32 for 0.001 resolution; or a Q-format like Q16.16 if wider range is needed later)
  - The driver/FPGA do not need IEEE-754; they only scale integers in/out per the command’s spec

Action: approve that S001 and U001–U006 carry 16-bit values in [4..5] (and [6..7] if a second value exists), with zero padding elsewhere. We’ll add a small table per command indicating the integer scaling (if any).

## 5) U005/U006 GPIO semantics — HW constraints and practical definition
- Proposed meaning:
  - U005 (GPIO Input Channel): set logical channel index (0/1) and an enable bit; effect is to enable/disable sampling/reporting of that input channel in the FPGA logic and allow the driver to expose it (e.g., through A-series queries)
  - U006 (GPIO Output Channel): set logical channel index (0/1) and an enable bit; effect is to enable/disable the logical output driving path in the FPGA logic
- Important: if the FPGA’s pin direction is fixed at synthesis time (no runtime reconfiguration), these commands do NOT change physical pin direction. They only update logical enable/mask in the datapath.
- If runtime pin-direction control is not supported in the bitstream, we will:
  - keep U005/U006 as logical masks (no physical re-mux), and
  - document that they do not affect fixed hardware wiring

Action: confirm whether the bitstream supports runtime direction control. If not, we proceed with the “logical mask only” behavior and store values in FRAM for persistence.

## 6) Recommended init/usage flow
1. PC opens COM port (FTDI VCP). Driver initializes internal queues/buffers
2. FPGA on boot loads defaults; when FRAM is available, it loads persisted config (S001 address, S002 baud, U001–U006, etc.). For MVP, mirror this map in on-chip SRAM
3. Optional handshake: PC can issue an A-series “hello/version” query to verify link
4. System configuration:
   - Set S001 (slave address) if needed
   - Set S002 (baud rate). If baud changes, execute a coordinated soft reset/reopen sequence so both sides switch cleanly
5. Begin normal operation with non-blocking, asynchronous request/response and periodic polling for readiness

## 7) FRAM address map — pragmatic proposal
- FRAM has no wear-leveling concerns like Flash, but keep fields aligned for simplicity
- Proposed base map (little-endian fields; mirrored in SRAM for MVP):
  - 0x0000: Version (u16) — for future compatibility
  - 0x0002: S001 Slave Address (u8), 0x0003 reserved
  - 0x0004: S002 Baud Code (u16) — a coded enum or actual baud divider per FPGA design
  - 0x0006: U001 (u16)
  - 0x0008: U002 (u16)
  - 0x000A: U003 (u16)
  - 0x000C: U004 (u16)
  - 0x000E: U005 (u16) — e.g., bit0 enable, bit1 channel, or split into two u8
  - 0x0010: U006 (u16)
  - 0x0012–0x001F: reserved for alignment/expansion

Action: confirm this minimal map. Junkai can wire the same offsets in SRAM for MVP, and later switch to FRAM without changing the protocol.

## 8) Multi-device behavior and error cases — no deadlocks by design
- Addressing: each frame contains a slave address/ID. Only the device with a matching address processes the frame; others ignore it and immediately return to idle
- Wrong-address scenario: if PC intended address 0x01 but frame carries 0x10 and a device 0x10 exists, that device will process it. The PC will time out waiting for 0x01 and can retransmit. The channel remains free; no device “waits” unnecessarily
- CRC failure: any device that detects a bad CRC discards the frame silently (no ACK/NACK), keeping the line free. PC uses retry policy
- Disassembly/processing on device:
  1) Detect SOF → read Address/ID → read 12-byte payload → read CRC → expect EOF
  2) Compute CRC over [Address/ID + 12-byte payload]; compare
  3) If Address matches and CRC OK → enqueue for processing; else discard
  4) Processing uses only the 12-byte payload in buffers (per our spec); framing fields (header/Address/CRC/trailer) are not stored in the payload FIFOs

这里面 不要说 SRAM，因为我们最终要的是在 FRAM 里面修改

而且这个 FRAM 的地址选择，是 Junkai 这边自己选就好，只要 程序知道去哪里读和取这个数据 

 然后去 update 这个文档，"D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\RS485Driver_new_python_with_new_UI\protocol_design\RS485_Protocol_For_FPGA.md"

 要尽可能精简，不要那么长，删除不必要的部分，比如 FPGA 示例代码，因为我不负责 FGPA 的部分，我是做 RS485 这边，写的越多，越会容易被提出越多的问题。

 这里类似 
- S001 — Set Slave Address
  - Key: 'S','0','0','1'
  - Addressing: broadcast (ID address bits = 0)
  - Payload [4..7]: uint32 newAddress (1..31), little-endian; [8..11] = 0
  - Action: update the device’s runtime slave address to newAddress and store to FRAM

中间的 payload, 也不需要 4 个 bytes, 因为我们目前传输的值都比较小，integer 足够表示，改成 2 个

至于之后 可能希望有浮点数，也采用 Junkai 所说的 映射的方法，浮点数能映射成整数，这个等之后有需求再做


-----
GPIO 的 Enable/Disable 指的是 GPIO 的功能，要不要用这个功能，要就打开，不用就关

GPIO 的功能是我们定义好的，是数字输入输出，FPGA 这边软件控制即可


----
  - For each input byte: for 8 bits: mix = (crc ^ byte) & 0x01; crc >>= 1; if (mix) crc ^= 0x97; byte >>= 1

 这部分的 确认 无误，要写清楚啊

----
UART 是没有协议的，一个字节一个字节收

所以我们这个流程是，所有的 device 都在收RS485 总线上的数据

我们的设备 收到 头字节以后才开始读，不然不要读
收到第16个帧以后，确认是 尾字节，然后这部分结束

继续读后面的数据

同时验证 这之前读的整个 16帧的 CRC 的值

如果 CRC 不对的话，slave 要向 PC 发一条重发请求
如果 CRC 对，接下来看 address, slave address 对才会执行，如果不对就忽略 不执行（但这部分我有问题，会出现这种情况嘛？ CRC 对，但 address 不对，address 不是在 CRC 里面？ 如果address 不对，CRC 肯定不对啊）
而且如果 CRC 对，slave 就给 PC 发一条确认收到的信息，然后把值返回去

我理解，就和 PC 传下去的 Command 内容一致，只是 ID 里面的 code 是 0b010 而不是 0b111

这个执行的流程 要写清楚，可以在 markdown 文件里 画个简单的示意图
----
我手工改了一些内容，
请按照目前的内容，再优化一下

Resend的内容， 具体的格式是什么？

就是 ID 改成 0b000 然后 address 是这个 slave address 其他的 payload 都是 0 这样？

还有 acknowledge 收到的这个，我理解 发回去的，最好就是 payload 是一样的内容，只是 ID 从 0b111 变成 0b010 对吧
比如收到的是 aa-e0-53-30-30-31-01-00-00-00-00-00-00-00-0b-0d
那么就是把这里 ID 里 code 的部分改成 b010 然后再传给 PC

把我们 所有的 command 传入 slave 的 这个格式，和 slave acknowledge 的格式写清楚

binary 和 hex 的都要

另外 PC 这个传入 数据的时候，已经 对数值范围做了一个检查，slave 这个还需要二次检查嘛？要的话，请把我们的设置范围也写清楚，可以看设计文档里，每个 command 的范围

另外流程图里，写清楚，我们设置 slave address 的时候，RS485 总线只能连接我们这一个设备