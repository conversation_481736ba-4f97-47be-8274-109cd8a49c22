# RS485 PC–FPGA Communication Protocol (MVP Version)
#### January 2025 Version

This document defines a streamlined protocol for PC-side RS485 driver communication with FPGA devices. Designed for MVP development with minimal workload and concise protocol specification.

## Supported Command Scope
- System commands: S001 (Set slave address)
- User commands: U001–U006 (Device configuration parameters)
- 12-byte payload: First 4 bytes ASCII command key, last 8 bytes data (little-endian)
- **Bidirectional communication**: PC sends commands, FPGA must return acknowledgment
- FRAM persistent storage

## 1. Physical and Link Layer
- **Medium**: RS485 (2-wire A/B)
- **Baud rate**: 9600
- **UART framing**: 8N1 (8 data bits, no parity, 1 stop), LSB-first; bytes arrive serially and are processed as a byte stream

- **Frame structure**: 16 bytes fixed length
  - Header: 1 byte = 0xAA
  - ID byte: 1 byte = [Function code(3 bits) | Slave address(5 bits)]
    - PC sends command: 0b111 (Assign Data)
    - FPGA returns acknowledgment: 0b010 (Response to Assign)
    - Slave address range: 1..31; broadcast uses 0x00
  - Payload: 12 bytes (see section 2)
  - CRC8: 1 byte — polynomial 0x97, initial 0x00, covers 13 bytes (ID + payload)
  - Trailer: 1 byte = 0x0D

**Important**: CRC8 covers only 13 bytes (ID byte + 12-byte payload), excluding header and trailer.

## 2. Payload Format (12 bytes)
- Bytes [0..3]: ASCII command key, e.g., 'S','0','0','1' or 'U','0','0','1'
- Bytes [4..11]: 8 bytes binary data, **little-endian encoding**
  - **Single 16-bit parameter**: Use bytes [4..5]; bytes [6..11] = 0
  - **Dual 16-bit parameters** (e.g., GPIO channel+enable): Use bytes [4..5] and [6..7]; bytes [8..11] = 0
  - **Maintain 12-byte payload**: For protocol uniformity, always use 12-byte payload

**Data type note**: Current parameter values are small, 16-bit integers are sufficient. For floating-point needs, use fixed-point mapping.


### Endianness (MVP)
- All numeric fields use little-endian byte order.
- Rationale: PC (x86/x64/Windows on ARM) and typical FPGA/MCU soft-cores are little-endian; this minimizes swaps and logic.

## 3. Command Set (S001, U001–U006)

PC sends commands using function code 0b111 (Assign Data). FPGA must return acknowledgment after receiving commands, using function code 0b010 (Response to Assign).

ACK payload: The FPGA must echo exactly the 12-byte payload it received (ASCII key + data bytes). The PC verifies that the echoed payload matches what was sent. If mismatch or timeout, the PC retransmits the command.


### 3.1 Request and Acknowledgment Frame Formats (PC → Slave, Slave → PC)
- Request (Assign Data):
  - Header 0xAA | ID (function 0b111 + 5-bit address) | 12-byte payload | CRC8 | Trailer 0x0D
  - ID binary: b111 aaaaa (aaa = 5-bit address)
- Acknowledgment (Response to Assign):
  - Bus requirement: during S001 address assignment, only this single device must be connected to the RS485 bus (no other slaves attached).

  - Header 0xAA | ID (function 0b010 + same 5-bit address as request) | 12-byte payload (exact echo) | CRC8 | Trailer 0x0D
  - ID binary: b010 aaaaa

Example (based on a broadcast S001):
- Request (PC → Slave), hex bytes:
  AA E0 53 30 30 31 01 00 00 00 00 00 00 00 CC 0D
  - AA = Header
  - E0 = ID (b111 00000): function 0b111, address 0 (broadcast)
  - 53 30 30 31 = ASCII 'S001'
  - 01 00 00 00 00 00 00 00 = payload data (newAddress=1 in [4..5], rest zero)
  - CC = CRC8 over [ID + 12-byte payload] (recalculate per Section 4)
  - 0D = Trailer

- ACK (Slave → PC), hex bytes:
  AA 40 53 30 30 31 01 00 00 00 00 00 00 00 XX 0D
  - 40 = ID (b010 00000): function 0b010, address 0 (same address bits as request)
  - Payload is an exact echo of the 12-byte payload received
  - XX = CRC8 over [new ID + same 12-byte payload]

Note: CRC values shown as CC/XX must be computed by both sides using the algorithm in Section 4. The ACK’s CRC changes because the ID changes.

### 3.2 Re-send Request Frame (Slave → PC)
- Purpose: notify the PC to retransmit the most recent command when the slave detects a CRC error.
- Frame format (MVP):
  - Header 0xAA | ID (function 0b000 + this device’s 5-bit address) | 12 bytes all 0x00 | CRC8 | Trailer 0x0D
  - ID binary: b000 aaaaa
- Example for device address 0x01, hex bytes:
  AA 01 00 00 00 00 00 00 00 00 00 00 00 00 YY 0D
  - 01 = ID (b000 00001): function 0b000, address 1
  - Twelve 0x00 bytes as payload
  - YY = CRC8 over [ID + 12 zero bytes]

Notes:
- The slave MUST send the re‑send request immediately after a CRC failure and MUST NOT execute the command.
- The PC, upon receiving this frame or upon timeout, retransmits the last command.

### 3.3 Parameter Validation on Slave (MVP)
- The PC performs range checks before sending; the slave performs the same checks as a safety guard.
- If values are out of range:
  - The slave does not execute and does not ACK (silent ignore). The PC will timeout and may alert the user.
- Valid ranges (uint16 unless noted):
  - S001 newAddress: 1..31
  - U001 threshold_mA: 40..500
  - U002 amplitude_mA: 1000..2000
  - U003 count: 1..5
  - U004 duration_ms: one of {200, 400, 600, 800, 1000}
  - U005 channel: {0,1}; enable: {0,1}
  - U006 channel: {0,1}; enable: {0,1}

### System Configuration Commands
- **S001 — Set Slave Address**
  - Key: 'S','0','0','1'
  - Addressing: broadcast (ID address bits = 0)
  - Payload [4..5]: uint16 newAddress (1..31), little-endian; [6..11] = 0
  - Action: update device runtime slave address and store to FRAM

### User Configuration Commands
- **U001 — Set SEL Threshold Current (mA)**
  - Key: 'U','0','0','1'
  - Payload [4..5]: uint16 threshold_mA (40..500); [6..11] = 0
  - Action: store to FRAM; apply to SEL logic

- **U002 — Set SEL Max Amplitude (mA)**
  - Key: 'U','0','0','2'
  - Payload [4..5]: uint16 amplitude_mA (1000..2000); [6..11] = 0
  - Action: store to FRAM; apply to SEL logic

- **U003 — Set SEL Detection Count**
  - Key: 'U','0','0','3'
  - Payload [4..5]: uint16 count (1..5); [6..11] = 0
  - Action: store to FRAM; consecutive detections before triggering power cycle

- **U004 — Set Power Cycle Duration (ms)**
  - Key: 'U','0','0','4'
  - Payload [4..5]: uint16 duration_ms, must be one of {200, 400, 600, 800, 1000}; [6..11] = 0
  - Action: store to FRAM; used for power cycle control

- **U005 — Configure GPIO Input Channel**
  - Key: 'U','0','0','5'
  - Payload [4..5]: uint16 channel (0 or 1)
  - Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0
  - Action: store to FRAM; apply to GPIO input configuration

- **U006 — Configure GPIO Output Channel**
  - Key: 'U','0','0','6'
  - Payload [4..5]: uint16 channel (0 or 1)
  - Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0
  - Action: store to FRAM; apply to GPIO output configuration

**GPIO Function Note**: GPIO Enable/Disable toggles the logical functionality (digital input/output path) under FPGA software control. It does NOT change physical pin direction if the bitstream fixes it at synthesis time. If runtime pin-direction control is not supported, U005/U006 act as logical masks only. Values are persisted in FRAM.

## 4. CRC8 Calculation (polynomial 0x97)
- **Coverage**: 13 bytes (ID byte + 12-byte payload)
- **Initial value**: 0x00
- **Polynomial**: 0x97 ⇒ G(x) = x^8 + x^7 + x^4 + x^2 + x + 1 (bit-reflected form for LSB-first algorithm)
- **Algorithm** (LSB first):
  ```
  For each input byte:
    For 8 bits:
      mix = (crc ^ byte) & 0x01
      crc >>= 1
      if (mix) crc ^= 0x97
      byte >>= 1
  ```

PC driver uses this algorithm, FPGA must implement the same algorithm to ensure compatibility.

## 5. Communication Flow

```
PC Side                        FPGA Side
  |                              |
  |------ Send Command ------>   |
  |    (Function 0b111)          |
  |                              |-- Receive 16 bytes
  |                              |-- Verify header(0xAA) and trailer(0x0D)
  |                              |-- Calculate and verify CRC8
  |                              |
  |                              |-- CRC error?
  |   <---- Resend Request ------|   Yes: Send function 0b000 (re-send request)
  |                              |
  |                              |-- CRC correct?
  |                              |   Yes: Check address
  |                              |
  |                              |-- Address match?
  |                              |   Yes: Execute command
  |                              |   No: Ignore
  |                              |
  |   <---- Return ACK ---------|-- Send acknowledgment
  |    (Function 0b010)          |   (Contains received command)
  |                              |
  |-- Verify returned content    |
  |-- Content correct: Complete  |
  |-- Content error/timeout: Resend |
```
### Recommended Init/Usage Flow (MVP)
1. PC opens COM port (FTDI VCP) and initializes buffers.
2. FPGA powers up with defaults; loads persisted config from FRAM (S001, U001–U006).
3. Optional: PC issues an A-series hello/version to verify link.
4. Configure system:
   - S001: set slave address if needed (broadcast ID address bits = 0)
5. Normal operation: non-blocking request/ACK loop with retry on timeout.

### Multi-device Behavior and Errors
- Addressing: only the device whose address matches the ID byte processes the command; others ignore it and stay idle.
- Wrong-address: PC times out waiting for the intended device; retransmit as needed. The bus remains free.
- CRC failure: device sends re-send request (function code 0b000) immediately; PC retransmits the last command.


### Key Process Steps:
1. **Frame Sync**: All devices monitor RS485 bus, start reading only after header (0xAA)
2. **Complete Reception**: Read full 16 bytes, confirm trailer (0x0D)
3. **CRC Verification**: Calculate 13-byte CRC, send resend request if mismatch
4. **Address Filtering**: After CRC passes, check address, only matching devices execute
5. **Acknowledgment Response**: After executing command, FPGA must send acknowledgment to PC
6. **Content Verification**: PC verifies returned command content to ensure correct communication

## 6. FRAM Storage
- **Purpose**: Persistent storage of all configuration parameters
- **Address allocation**: Decided by FPGA side (Junkai) for address mapping
- **Storage content**:
  - S001 slave address
  - U001-U006 all user configuration parameters
- **Data format**: little-endian, consistent with protocol
- **Power-on loading**: FPGA reads all configuration parameters from FRAM at startup

## 7. Error Handling
- **CRC error**: FPGA sends resend request (function code 0b000)
- **Address mismatch**: Silent ignore (no response sent)
- **Parameter out of range**: Silent ignore or send error response
- **Timeout handling**: PC resends command after timeout

## 8. Implementation Points
1. **UART Reception**: Parse 16-byte frame, confirm header and trailer
2. **CRC Calculation**: Calculate CRC8 over 13 bytes (ID + payload)
3. **Function Code Processing**:
   - Receive: 0b111 (Assign Data)
   - Send: 0b010 (Response to Assign)
4. **Address Matching**: Extract lower 5 bits of ID byte, compare with device address
5. **Payload Parsing**: Extract ASCII key and binary data
6. **FRAM Operation**: Immediately store configuration parameters
7. **Response Sending**: Construct acknowledgment frame and send

This protocol is MVP version, maintaining minimal complexity to ensure minimum development workload for both PC and FPGA sides.
