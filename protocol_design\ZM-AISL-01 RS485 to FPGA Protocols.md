ZM-AISL-01 RS485 to FPGA Protocols
Supported Command Scope
• System commands: S001 (Set slave address)
• User commands: U001–U006 (Device configuration parameters)
• 12-byte payload: First 4 bytes ASCII command key, last 8 bytes data (little-endian)
• Mater-slave communication: <PERSON>(Master) sends commands, <PERSON>P<PERSON>(slave) must return with either acknowledgment or response frame or re-send request. FPGA should always listen RS485 BUS for a command from Master and response the command received, never send anything proactively without receiving a command addressed to itself.  Remember, there might be other slave device connected in the same RS485 BUS (share the same hardware BUS), therefore, all slaves have to follow the same communication protocol to avoid data collision. 
• FRAM persistent storage
1. Physical and Link Layer
• Medium: RS485 (2-wire A/B)
• Baud rate: 9600
• UART framing: 8N1 (8 data bits, no parity, 1 stop), LSB-first; bytes arrive serially and are processed as a byte stream
• Frame structure: 16 bytes fixed length
o Header: 1 byte = 0xAA
o ID byte: 1 byte = [Function code (3 bits) |  Address (5 bits)]
o Function code (higher 3 bits) field of ID byte:
 PC sends command: 0b111 (Mater Assign Data)
 PC sends command: 0b110 (Mater request Data)
 FPGA acknowledge: 0b010 (Response to Assignment)
 FPGA return request: 0b001 (Response to Request)
 Both FPGA and Mater re-send request: 0b000 (Re-send request)
o Address (lower 5 bits) field of ID byte:
 Mater address is fixed at 0b00001
 Broadcasting frame is always with address field = 0b00000
 Slave address use 0b00002 to 0b11111 (2 to 31) which will be assigned by the Master.
o Payload: 12 bytes (see section 2)
o CRC8: 1 byte — polynomial 0x97, initial 0x00, covers 13 bytes (ID + payload)
o Trailer: 1 byte = 0x0D
Important: CRC8 covers only 13 bytes (ID byte + 12-byte payload), excluding header and trailer.
2. Payload Format (12 bytes)
• Bytes [0..3]: ASCII command key, e.g., 'S','0','0','1' or 'U','0','0','1'
• Bytes [4..11]: 8 bytes binary data, little-endian encoding
o Single 16-bit parameter: Use bytes [4..5]; bytes [6..11] = 0
o Dual 16-bit parameters (e.g., GPIO channel + enable): Use bytes [4..5] and [6..7]; bytes [8..11] = 0
o Maintain 12-byte payload: For protocol uniformity, always use 12-byte payload
Data type note: Current parameter values are small, 16-bit integers are sufficient. For floating-point needs, use fixed-point mapping (e.g., actual value × 1000 stored as integer).
Endianness (MVP)
• All numeric fields use little-endian byte order.
• Rationale: PC (x86/x64/Windows on ARM) and typical FPGA/MCU soft-cores are little-endian; this minimizes byte swaps and logic complexity in both PC driver and FPGA implementation.
• Example: Value 250 (0x00FA) is stored as [0xFA, 0x00] in bytes [4..5]
3. Command Set (S001, U001–U006)
3.1 FPGA action upon receiving commands:
Upon receiving an assigning command sent by PC with function code 0b111 (Assigning Data). FPGA must return an acknowledgment frame after receiving the commands without error by using function code 0b010 (Response to Assign) and ACK payload.
ACK payload: The FPGA must echo exactly the 12-byte payload it received (ASCII key + data bytes). The PC verifies that the echoed payload matches what was sent. If mismatch or timeout, the PC retransmits the command.
Upon receiving a request command sent by PC with function code 0b110 (Request Data). FPGA must return a response frame after receiving the commands without error by using function code 0b001 (Response to request) and Response payload.
Response payload: FPGA should send a response payload with the same key field as in the request command received, however, data field should be assembled with corresponding data according to the key field of the request command received from the Master.

3. 2 System Configuration Commands
• S001 — Set Slave Address
o ID byte: 0b11100000, this is broadcasting frame ID for assignment.
o Key: 'S','0','0','1' in ASCII code.
o Payload [4..5]: uint16 new Address (2..31), little-endian; [6..11] = 0
o Action: update device runtime slave address and store to FRAM
o Important: Only connect ONE slave device (ZM-AISL-01 in our case) to Master in RS485 BUS during sending S001 address assignment command.
User Configuration Commands
• U001 — Set SEL Threshold Current (mA)
o ID byte: 0b111xxxxx, this is assignment frame ID, xxxxx represent slave address.
o Key: 'U','0','0','1' in ASCII code.
o Payload [4..5]: uint16 threshold_mA (40..500); [6..11] = 0
o Action: store to FRAM; apply to SEL logic
• U002 — Set SEL Max Amplitude (mA)
o ID byte: 0b111xxxxx, this is assignment frame ID, xxxxx represent slave address.
o Key: 'U','0','0','2' in ASCII code
o Payload [4..5]: uint16 amplitude_mA (1000..2000); [6..11] = 0
o Action: store to FRAM; apply to SEL logic
• U003 — Set SEL Detection Count
o ID byte: 0b111xxxxx, this is assignment frame ID, xxxxx represent slave address.
o Key: 'U','0','0','3' in ASCII code.
o Payload [4..5]: uint16 count (1..5); [6..11] = 0
o Action: store to FRAM; consecutive detections before triggering power cycle
• U004 — Set Power Cycle Duration (ms)
o ID byte: 0b111xxxxx, this is assignment frame ID, xxxxx represent slave address.
o Key: 'U','0','0','4'
o Payload [4..5]: uint16 duration_ms, must be one of {200, 400, 600, 800, 1000}; [6..11] = 0
o Action: store to FRAM; used for power cycle control
• U005 — Configure GPIO Input Channel
o ID byte: 0b111xxxxx, this is assignment frame ID, xxxxx represent slave address.
o Key: 'U','0','0','5'
o Payload [4..5]: uint16 channel (1 or 2), represent hardware channel.
o Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0
o Action: store to FRAM; perform power cycle according to user input when corresponding channel SEL is detected.
• U006 — Configure GPIO Output Channel
o ID byte: 0b111xxxxx, this is assignment frame ID, xxxxx represent slave address.
o Key: 'U','0','0','6'
o Payload [4..5]: uint16 channel (1 or 2), representing hardware channel.
o Payload [6..7]: uint16 enable (0=disable, 1=enable); [8..11] = 0
o Action: store to FRAM; Perform user output when corresponding channel SEL detected before and after power cycle. 
GPIO Function Note: GPIO pins connected to FPGA do not change direction during run time. Digital inputs / outputs states of the pins are fixed in Verilog code and during synthesis.  
• U007 — Request SEL detection data log of channel 1 
o ID byte: 0b110xxxxx, this is requesting frame ID, xxxxx represent slave address.
o Key: 'U','0','0','7' in ASCII code.
o Payload [4..11]: payload [4..11] = 0 
o Action: Read Channel 1 SEL log data from FRAM, send a response frame to Master. 
• U008 — Request SEL detection data log of channel 2
o ID byte: 0b110xxxxx, this is requesting frame ID, xxxxx represent slave address.
o Key: 'U','0','0','8' in ASCII code.
o Payload [4..11]: payload [4..11] = 0 
o Action: Read Channel 2 SEL log data from FRAM, send a response frame to Master. 
• U009 — Request SEL detection data log of ZM-AISL-01
o ID byte: 0b110xxxxx, this is requesting frame ID, xxxxx represent slave address.
o Key: 'U','0','0','9' in ASCII code.
o Payload [4..11]: payload [4..11] = 0 
o Action: Read self-system SEL log data from FRAM, send a response frame to Master. 

• U0010 — Request data U00X from FPGA to PC
o ID byte: 0b110xxxxx, this is requesting frame ID, xxxxx represent slave address.
o Key: 'U','0','0','x' in ASCII code.  Where x = ‘1’ ….’6’
o Payload [4..11]: payload [4..11] = 0 
Action: Read U00X data from FRAM, send a response frame to Master.


Parameter Range Validation Table
Both PC and FPGA must validate parameters against these ranges. FPGA silently ignores commands with out-of-range parameters.
Command Parameter Data Type Valid Range Invalid Behavior
S001  Slave Address uint16  1 to 31 FPGA ignores (silent)
U001  SEL Threshold Current uint16  40 to 500 (mA)  FPGA ignores (silent)
U002  SEL Max Amplitude uint16  1000 to 2000 (mA) FPGA ignores (silent)
U003  SEL Detection Count uint16  1 to 5  FPGA ignores (silent)
U004  Power Cycle Duration  uint16  {200, 400, 600, 800, 1000} (ms) FPGA ignores (silent)
U005  GPIO Input Channel  uint16  0 or 1  FPGA ignores (silent)
U005  GPIO Input Enable uint16  0 or 1  FPGA ignores (silent)
U006  GPIO Output Channel uint16  0 or 1  FPGA ignores (silent)
U006  GPIO Output Enable  uint16  0 or 1  FPGA ignores (silent)
Note: Parameter validation occurs after CRC verification and address matching. Invalid parameters result in silent ignore (no ACK sent), causing PC timeout. User must manually correct values and resend via GUI interface.
4. CRC8 Calculation (polynomial 0x97)
• Coverage: 13 bytes (ID byte + 12-byte payload)
• Initial value: 0x00
• Polynomial: 0x97 ⇒ G(x) = x^8 + x^7 + x^4 + x^2 + x + 1
• Algorithm (LSB first):
• For each input byte:
•   For 8 bits:
•     mix = (crc ^ byte) & 0x01
•     crc >>= 1
•     if (mix) crc ^= 0x97
•     byte >>= 1
PC driver uses this algorithm, FPGA must implement the same algorithm to ensure compatibility.
Important note: When FPGA implement CRC check, serial data stream used in calculation should be 8 bit data for each byte excluding start bit and stop bit of the byte transmission frame.  
5. Communication Flow
IMPORTANT for S001 Address Setting: When setting slave address using S001 command, ensure ONLY ONE slave device is conected to the RS485 bus to prevent address conflicts.
PC Side                             FPGA Side
  |                                     |
  |------ Send Command -------------->  |
  |    (Function 0b111)                 |
  |                                     |-- Receive 16 bytes
  |                                     |-- Verify header(0xAA) and trailer(0x0D)
  |                                     |-- Only proceed with correct                 
  |                                     |   header and trailer received, either 
  |                                     |   one incorrect, drop the frame.
  |                                     |-- Calculate and verify CRC8
  |                                     |
  |                                     |-- CRC error?
  |   <---- Resend Request ----------   |   Yes: Send frame with ID 
  |                                     |   byte = 0b00000001 re-send request
  |                                     |
  |                                     |-- CRC correct?
  |                                     |   Yes: Check address
  |                                     |
  |                                     |-- Address match own address?
  |                                     |   Yes: process the payload
  |                                     |   No: Ignore (silent)
  |                                     |
  |                                     |
  |   <---- Return ACK -------------    |-- Send acknowledgment
  |    (Function 0b010)                 |   (Echo received payload)
  |                                     |
  |-- Verify returned content           |
  |-- Content correct: Complete         |
  |-- Content error/timeout: Resend     |
Key Process Steps:
1.  Frame Sync: All slave devices listen to RS485 bus, start reading 16 bytes frame only after header byte (0xAA) is currently received. 
2.  Complete Reception: Read full 16 bytes, confirm trailer byte (0x0D) correctly received, then proceed with next step. 
3.  CRC Verification: Calculate 13-byte data (ID + payload) with CRC byte together, send re-send request frame if CRC check error.
4.  Address Filtering: if CRC check passes, check address, only process payload when received address matching with own address, or else, drop the frame just received. 
5.  Check Function code in ID byte: to execute the command according to the function code received. 
6.  Command Execution: action/process payload accordingly.
7.  Acknowledgment Response: Send acknowledgment with echoed payload to PC
FPGA Decision Flowchart
Start: Monitor RS485 Bus
         |
         v
    Detect Header (0xAA)?
         |
    No ──┴── Continue Monitoring
         |
    Yes  v
    Read 16 bytes complete?
         |
    No ──┴── Discard & Continue Monitoring
         |
    Yes  v
    Trailer (0x0D) correct?
         |
    No ──┴── Discard & Continue Monitoring
         |
    Yes  v
    Calculate CRC8 over 13 bytes
         |
         v
    CRC8 matches?
         |
    No ──┴── Send Resend Request (Function 0b000)
         |    └── Continue Monitoring
    Yes  v
    Extract address from ID byte
         |
         v
    Address matches this device?
         |
    No ──┴── Silent Ignore & Continue Monitoring
         |
    Yes  v
    Extract parameters from payload
         |
         v
    Parameters within valid range?
    (Check Parameter Range Validation Table)
         |
    No ──┴── Silent Ignore & Continue Monitoring
         |
    Yes  v
    Execute Command:
    - Store to FRAM
    - Apply settings
         |
         v
    Send ACK (Function 0b010):
    - Echo received payload
    - Calculate new CRC8
         |
         v
    Continue Monitoring
Manual Resend Process: When PC times out (no ACK received), user must manually resend command via GUI interface. This allows user to correct invalid parameter values and prevents automatic retry loops that could cause deadlock situations.
6. Frame Format Examples
PC Command Frame (Function 0b111)
Example: S001 set slave address to 1
• Binary: 10101010 11100000 01010011 00110000 00110000 00110001 00000001 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00001011 00001101
• Hex: AA E0 53 30 30 31 01 00 00 00 00 00 00 00 0B 0D
• Breakdown:
o Header: 0xAA
o ID: 0xE0 (0b11100000 = Function 0b111 + Address 0b00000)
o Payload: "S001" + value 1 (little-endian) + padding zeros
o CRC8: 0x0B (calculated over 13 bytes: E0 53 30 30 31 01 00 00 00 00 00 00 00)
o Trailer: 0x0D
Example: U001 set threshold to 250mA
• Binary: 10101010 11100001 01010101 00110000 00110000 00110001 11111010 00000000 00000000 00000000 00000000 00000000 00000000 00000000 01001100 00001101
• Hex: AA E1 55 30 30 31 FA 00 00 00 00 00 00 00 4C 0D
• Breakdown:
o Header: 0xAA
o ID: 0xE1 (0b11100001 = Function 0b111 + Address 0b00001)
o Payload: "U001" + value 250 (0x00FA little-endian) + padding zeros
o CRC8: 0x4C
o Trailer: 0x0D
Example: U005 enable GPIO input channel 0
• Binary: 10101010 11100001 01010101 00110000 00110000 00110101 00000000 00000000 00000001 00000000 00000000 00000000 00000000 00000000 01001111 00001101
• Hex: AA E1 55 30 30 35 00 00 01 00 00 00 00 00 4F 0D
• Breakdown:
o Header: 0xAA
o ID: 0xE1 (0b11100001 = Function 0b111 + Address 0b00001)
o Payload: "U005" + channel 0 (0x0000) + enable 1 (0x0001) + padding zeros
o CRC8: 0x4F
o Trailer: 0x0D
Example: U006 disable GPIO output channel 1
• Binary: 10101010 11100001 01010101 00110000 00110000 00110110 00000001 00000000 00000000 00000000 00000000 00000000 00000000 00000000 01010000 00001101
• Hex: AA E1 55 30 30 36 01 00 00 00 00 00 00 00 50 0D
• Breakdown:
o Header: 0xAA
o ID: 0xE1 (0b11100001 = Function 0b111 + Address 0b00001)
o Payload: "U006" + channel 1 (0x0001) + enable 0 (0x0000) + padding zeros
o CRC8: 0x50
o Trailer: 0x0D
FPGA Acknowledgment Frame (Function 0b010)
Example: ACK for S001 command
• Binary: 10101010 01000000 01010011 00110000 00110000 00110001 00000001 00000000 00000000 00000000 00000000 00000000 00000000 00000000 01101011 00001101
• Hex: AA 40 53 30 30 31 01 00 00 00 00 00 00 00 6B 0D
• Breakdown:
o Header: 0xAA
o ID: 0x40 (0b01000000 = Function 0b010 + Address 0b00000)
o Payload: Echo of received payload (identical to PC command)
o CRC8: 0x6B
o Trailer: 0x0D
FPGA Resend Request Frame (Function 0b000)
Example: Resend request from slave address 1
• Binary: 10101010 00000001 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000001 00001101
• Hex: AA 01 00 00 00 00 00 00 00 00 00 00 00 00 01 0D
• Breakdown:
o Header: 0xAA
o ID: 0x01 (0b00000001 = Function 0b000 + Address 0b00001)
o Payload: All zeros (12 bytes)
o CRC8: 0x01
o Trailer: 0x0D
Recommended Init/Usage Flow (MVP)
1.  PC opens COM port (FTDI VCP) and initializes buffers.
2.  FPGA powers up with defaults; loads persisted config from FRAM (S001, U001–U006).
3.  Configure system:
• S001: set slave address (IMPORTANT: Only ONE slave device connected during address assignment)
4.  Normal operation: non-blocking request/ACK loop with retry on timeout.
Multi-device Behavior and Errors
• Addressing: only the device whose address matches the ID byte processes the command; others ignore it and stay idle.
• Wrong-address: PC times out waiting for the intended device; retransmit as needed. The bus remains free.
• CRC failure: device sends re-send request (function code 0b000) immediately; PC retransmits the last command.
7. FRAM Storage
• Purpose: Persistent storage of all configuration parameters
• FRAM Address allocation: FRAM address allocation will be decided by FPGA side (Junkai), currently have 9 sets of data to be stored U001 – U009. Future may add more data (e.g. AI model weights) 
• Though all data sent from Mater are two bytes, some data can be stored as one byte data such as S001, U003, U005, U006, it is FPGA (Junkai)’s decision on how to store them in FRAM.
• Storage content:
o S001 self slave address
o U001-U009 data 
• Data format: little-endian, consistent with protocol
• Power-on loading: Upon power on, FPGA reads all user configuration data from FRAM to SRAM for operation.
8. Error Handling
• CRC error: FPGA sends resend request (function code 0b000)
• Address mismatch: Silent ignore (no response sent)
• Parameter out of range: Silent ignore (no response sent) - prevents deadlock scenarios
• Timeout handling: User must manually resend via GUI interface
Parameter Validation Strategy:
• PC performs primary range validation before sending commands
• FPGA performs secondary validation after CRC and address checks (see Parameter Range Validation Table)
• If FPGA detects invalid parameters, it silently ignores the command (no ACK, no resend request)
• PC will timeout, requiring user to manually correct values and resend via GUI
• Manual Resend Requirement: This prevents automatic retry loops and allows user to verify/correct parameter values, avoiding deadlock situations
9. Implementation Points
1.  UART Reception: Parse 16-byte frame, confirm header and trailer
2.  CRC Calculation: Calculate CRC8 over 13 bytes (ID + payload)
3.  Function Code Processing:
• Receive: 0b111 (Assign Data)
• Send: 0b010 (Response to Assign)
• Send: 0b000 (Re-send request)
4.  Address Matching: Extract lower 5 bits of ID byte, compare with device address
5.  Payload Parsing: Extract ASCII key and binary data
6.  FRAM Operation: Store configuration parameters to FRAM
7.  Response Sending: Construct acknowledgment frame and send
This protocol is MVP version, maintaining minimal complexity to ensure minimum development workload for both PC and FPGA sides.

