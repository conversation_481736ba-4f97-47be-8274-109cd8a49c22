## RS485 Communication Software Protocol

1. RS485 Overview
2. Specification
3. Protocol

## Revision History

1. RS485 Overview: The most common RS485 application is one twisted pair connected multiple device in BUS topology with half-duplex and master-slave type protocol. RS485 is a well-defined physical layer electrical protocol guaranteed by RS485 transceiver IC. Any protocols above RS485 can be selected/defined by system designer. This document will specify a simple ZES proprietary data link layer protocol.


Figure1. Single twisted pair in BUS topology, one master to multiple slaves
In our application, ZM-AISL-01 (FPGA) board is the slave(Device), the master(Host) is most likely a satellite OBC(On Borad Computer) or a pc. Communication will always be initiated by the master, FPGA is required to keep listening the RS485 BUS and response the master's request.

2. Communication protocol stack
2.1 ZM-AISL-01 use protocol adapted from OSI 7 layers stack model as below.

| Layer | OSI Model | ZM-AISL-01 |
| ---: | :--- | :--- |
| 7 | Application | User app + ZES Driver |
| 6 | Presentation | Not used |
| 5 | Session | Not used |
| 4 | Transport | Not used |
| 3 | Network | Not used |
| 2 | Data Link | ZES DataLink |
| 1 | Physical | EIA/TIA-485 |

With RS485 as physical layer protocol, ZES define a proprietary data link layer protocol, together with driver provided, user can communicate with ZM-AISL-01 without knowing details of the protocol.
2.2 Byte DATA format for physical layer:

We use the same byte data format as the UART as below, data will be sent in serial with LSB first.

Generic Serial Frame

in ZES protocol, we define stop bit =1 and no parity bit, so total 10 bit.
Data will always be transmitted and received in byte at pre-defined baud rate, one after another.
Note: When the serial BUS is idle (no driver is enabled), it is guaranteed by the transceiver IC to keep receiver reading logic high, therefore, to make sure a high to low transition of start bit detected by an UART controller which is implemented in FPGA.
2.3 Baud rate

Baud rate will be assigned by the master, the slave (FPGA) will use a default baud rate (9600) upon power on before assignment, than listen the broadcasting data frame to get assigned baud rate. Available optional baud rate will be 9600, 19200, 38400, 57600, 115200.

RS485 allow 32 devices connected on the BUS, each device has an unique address for identification. Most commonly, the master own address 0x01(so is ZES protocol), slave address can be assigned either by hardware (embedded) or by software(master). Since AISL board has on board FRAM, so we use the way of software address assignment. Note: all AISL boards will be initialized with a default address 0x00 during manufacturing.
To assign a user designated address to a particular AISL board, connect the AISL board alone (without other salves) to the master (PC in our case) and run ZES-link software. On board FPGA will listen a broadcasting data frame(addressed at 0x00) to obtain user assigned slave address, then FPGA shall write the address to FRAM, so when next power on, FPGA shall get its own address from FRAM.
2.4 Data Link layer protocol
(1) ZES Data frame (package): ZES defined below data frame for data link protocol as below.


Data link layer data frame
(2) Header and Trailer: We define 0xAA as header and 0x0D as trailer.
(3) Frame ID Byte: two field were defined as below


High 3 bit Low 5 bit
We define lower 5 bit of ID byte as address of data destination, higher 3 bit as function code. 5 bit address field allow maximum 31 nodes ( 30 slaves plus one master) and 0b00000 address is reserved for broadcast data frame. Higher 3 bits field are codes indicating data transfer function defined as below table.

| Code | Function | Remark |
| :--- | :--- | :--- |
| Ob111 | Assign data | Master use |
| Ob110 | Request data | Master use |
| Ob101 | Reserved |  |
| Ob100 | Reserved |  |
| 0b011 | Reserved |  |
| 0b010 | Response2 | Slave use when response Assign |
| 0b001 | Response1 | Slave use when response request |
| 0b000 | re-send request | Both master and slave use |

(4) CRC Byte: ZES DataLink protocol use CRC8 with polynomial $0 x 97=x^{8}+x^{5}+x^{3}+x^{2}+x+1$. Note: this CRC check and cover 13 bytes data from ID to data payload (excluding header byte) which means if any error bit occurred in header byte, the current frame will be dropped off automatically and lead a timeout re-send since receiver always check header byte as start byte of the frame. Both master and slave must calculate and send CRC value in outgoing(sending) frame(message), perform CRC verification check in incoming(receiving) frame. In case of error (CRC check none zero), immediately transmit a re-send request frame.
2.5 Application layer data format (Data payload): data payload consists of one Json data which is a key-value pair, the key takes higher 4 bytes and value takes lower 8 bytes.


This key-value pair can be considered as a message, representing either a variable or a parameter DATA to be exchanged between master and slave. The key field holds name of the variable in ASCII code, the value field holds value of the variable in binary.
ZES-link defined categories of message sending over the 485 link as below:
Note: this definition can be extended when needed.

| Key code | Category | Used for |
| :--- | :--- | :--- |
| "Wxxx" | Weights of AI model | Transmit weights parameters from PC to AISL |
| "Uxxx" | User configurable data | Allow user to configure/control how AISL work. |
| "Sxxx" | System configurable data | Assign AISL system parameters. |
| "Axxx" | Application related data | Application related information/status report. |

Each category message can be further defined and extended later by using "xxx".
2.6 Use of broadcast frame, currently ZES-link only use broadcast frame to assign slave address, however, user can define application related broadcast message, such as sleep and wake up, by sending "Uxxx" message to AISL.
3. Operation rules for avoiding data collisions.

In half duplex master-slave BUS topology communication, every node (including the master) shall obey following rules in order to avoid data collisions.
(1) It is master's responsibility to initiate communication, to control the BUS data flow, to manage BUS status (such as idle time). Slave node can only send data frame as response to master's request/query and only respond to those requests addressed to its own node.
(2) It is slave's responsibility to respond master's request within response window time (tentatively 100 ms is defined in ZES protocol), No data sent when response window time is lapsed.
(3) As a slave, only enable yourself node's transmitter when you are ready to send data, disable the transmitter immediately as soon as finish sending the stop bit where the receiver will be ready to listen the 485 BUS.
(4) Only the master can send broadcasting message. No responding required from slave to the broadcasting message except slave address assignment message.
(5) In case master do not receive a response from designated slave, it is master's discretion to send the request again, the case may be broken link or the slave node hardware failure.

